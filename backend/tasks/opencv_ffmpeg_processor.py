"""
优化的OpenCV + FFmpeg视频处理器 - 重构后的兼容性模块
专注于高效的OpenCV并行处理和FFmpeg音频合并
"""

# 导入重构后的模块
from .video_processing import (
    PerformanceConfig,
    SerializableProcessor,
    create_opencv_processor,
    process_frame_with_opencv,
    OpenCVFFmpegProcessor
)

# 保持向后兼容性
__all__ = [
    'PerformanceConfig',
    'SerializableProcessor', 
    'create_opencv_processor',
    'process_frame_with_opencv',
    'OpenCVFFmpegProcessor'
]

"""
优化的OpenCV + FFmpeg视频处理器
专注于高效的OpenCV并行处理和FFmpeg音频合并
"""

import cv2
import subprocess
import os
import time
import tempfile
import json
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Callable, Optional, Dict, Any, List, Tuple
import logging
import psutil
import numpy as np
import threading

logger = logging.getLogger(__name__)


class PerformanceConfig:
    """性能配置管理类"""

    def __init__(self):
        # 自动检测系统资源
        self.cpu_count = mp.cpu_count()
        self.available_memory = psutil.virtual_memory().available

        # 检测是否在Celery环境中运行
        self.is_celery_worker = self._detect_celery_environment()

        # 并行处理配置
        if self.is_celery_worker:
            # Celery环境：使用线程池，更保守的配置
            self.use_thread_pool = True
            self.max_workers = min(self.cpu_count, 6)  # 线程数可以稍多一些
            self.batch_size = 20  # 较小的批处理大小
        else:
            # 非Celery环境：使用进程池，更激进的配置
            self.use_thread_pool = False
            self.max_workers = min(self.cpu_count, 8)
            self.batch_size = 30

        self.memory_limit_mb = min(2048, self.available_memory // (1024 * 1024) // 4)  # 使用1/4可用内存

        # FFmpeg优化配置
        self.ffmpeg_threads = self.cpu_count
        self.enable_hardware_accel = True

        # 进度回调优化
        self.progress_update_interval = 10  # 每处理10帧更新一次进度

        pool_type = "ThreadPool" if self.use_thread_pool else "ProcessPool"
        env_type = "Celery" if self.is_celery_worker else "Standalone"
        logger.info(f"Performance config ({env_type}): {self.max_workers} workers ({pool_type}), "
                   f"batch_size={self.batch_size}, memory_limit={self.memory_limit_mb}MB")

    def _detect_celery_environment(self) -> bool:
        """检测是否在Celery worker环境中运行"""
        try:
            # 检查当前进程是否是daemon进程
            current_process = mp.current_process()
            if hasattr(current_process, 'daemon') and current_process.daemon:
                return True

            # 检查环境变量
            if 'CELERY_WORKER' in os.environ:
                return True

            # 检查进程名称
            if 'celery' in current_process.name.lower():
                return True

            return False
        except Exception:
            return False

    def adjust_for_video_size(self, width: int, height: int, total_frames: int):
        """根据视频尺寸调整配置"""
        # 估算单帧内存使用（RGB，3通道）
        frame_size_mb = (width * height * 3) / (1024 * 1024)

        # 调整批处理大小以控制内存使用
        max_batch_by_memory = max(1, int(self.memory_limit_mb / (frame_size_mb * self.max_workers)))
        self.batch_size = min(self.batch_size, max_batch_by_memory)

        # 对于大视频，减少并行度
        if frame_size_mb > 10:  # 大于10MB的帧
            self.max_workers = min(self.max_workers, 4)

        logger.info(f"Adjusted config for {width}x{height}: "
                   f"batch_size={self.batch_size}, workers={self.max_workers}")


class SerializableProcessor:
    """可序列化的OpenCV处理器类"""

    def __init__(self, operation_type: str, **parameters):
        self.operation_type = operation_type
        self.parameters = parameters

    def process(self, frame: np.ndarray) -> np.ndarray:
        """处理单帧"""
        if self.operation_type == 'grayscale':
            return cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        elif self.operation_type == 'resize':
            width = self.parameters.get('width', 640)
            height = self.parameters.get('height', 480)
            return cv2.resize(frame, (width, height))

        elif self.operation_type == 'binary':
            threshold = self.parameters.get('threshold', 127)
            max_value = self.parameters.get('max_value', 255)
            threshold_type = self.parameters.get('threshold_type', 'binary')

            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            if threshold_type == 'binary':
                _, binary_frame = cv2.threshold(gray_frame, threshold, max_value, cv2.THRESH_BINARY)
            elif threshold_type == 'binary_inv':
                _, binary_frame = cv2.threshold(gray_frame, threshold, max_value, cv2.THRESH_BINARY_INV)
            else:
                _, binary_frame = cv2.threshold(gray_frame, threshold, max_value, cv2.THRESH_BINARY)

            return binary_frame

        elif self.operation_type == 'blur':
            filter_type = self.parameters.get('filter_type', 'gaussian')
            kernel_size = self.parameters.get('kernel_size', 15)
            sigma = self.parameters.get('sigma', 0)

            # 确保kernel_size为奇数
            if kernel_size % 2 == 0:
                kernel_size += 1

            if filter_type == 'gaussian':
                return cv2.GaussianBlur(frame, (kernel_size, kernel_size), sigma)
            elif filter_type == 'median':
                return cv2.medianBlur(frame, kernel_size)
            elif filter_type == 'bilateral':
                return cv2.bilateralFilter(frame, kernel_size, 75, 75)
            else:
                return cv2.GaussianBlur(frame, (kernel_size, kernel_size), sigma)

        elif self.operation_type == 'edge_detection':
            low_threshold = self.parameters.get('low_threshold', 50)
            high_threshold = self.parameters.get('high_threshold', 150)
            edge_type = self.parameters.get('edge_type', 'canny')

            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            if edge_type == 'canny':
                edges = cv2.Canny(gray_frame, low_threshold, high_threshold)
            elif edge_type == 'sobel':
                sobelx = cv2.Sobel(gray_frame, cv2.CV_64F, 1, 0, ksize=3)
                sobely = cv2.Sobel(gray_frame, cv2.CV_64F, 0, 1, ksize=3)
                edges = np.sqrt(sobelx**2 + sobely**2)
                edges = np.uint8(edges / edges.max() * 255)
            elif edge_type == 'laplacian':
                edges = cv2.Laplacian(gray_frame, cv2.CV_64F)
                edges = np.uint8(np.absolute(edges))
            else:
                edges = gray_frame

            return edges

        elif self.operation_type == 'transform':
            transform_type = self.parameters.get('transform_type', 'rotate_90')
            angle = self.parameters.get('angle', 90)

            height, width = frame.shape[:2]

            if transform_type == 'rotate_90':
                return cv2.rotate(frame, cv2.ROTATE_90_CLOCKWISE)
            elif transform_type == 'rotate_180':
                return cv2.rotate(frame, cv2.ROTATE_180)
            elif transform_type == 'rotate_270':
                return cv2.rotate(frame, cv2.ROTATE_90_COUNTERCLOCKWISE)
            elif transform_type == 'flip_horizontal':
                return cv2.flip(frame, 1)
            elif transform_type == 'flip_vertical':
                return cv2.flip(frame, 0)
            elif transform_type == 'flip_both':
                return cv2.flip(frame, -1)
            elif transform_type == 'rotate_custom':
                center = (width // 2, height // 2)
                rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
                return cv2.warpAffine(frame, rotation_matrix, (width, height))
            else:
                return cv2.rotate(frame, cv2.ROTATE_90_CLOCKWISE)

        else:
            # 默认返回原始帧
            return frame


def process_frame_with_opencv(frame_data: Tuple[np.ndarray, SerializableProcessor, int]) -> Tuple[int, np.ndarray]:
    """
    并行处理单帧的工作函数（进程池版本）
    Args:
        frame_data: (frame, processor, frame_index)
    Returns:
        (frame_index, processed_frame)
    """
    frame, processor, frame_index = frame_data

    try:
        # 使用OpenCV处理帧
        processed_frame = processor.process(frame)

        # 确保输出格式正确
        if len(processed_frame.shape) == 2:
            processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
        elif len(processed_frame.shape) == 3 and processed_frame.shape[2] == 4:
            processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_BGRA2BGR)

        return frame_index, processed_frame

    except Exception as e:
        logger.error(f"Frame {frame_index} processing error: {e}")
        # 出错时返回原始帧
        if len(frame.shape) == 2:
            frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
        return frame_index, frame


class ThreadSafeFrameProcessor:
    """线程安全的帧处理器"""

    def __init__(self, processor: SerializableProcessor):
        self.processor = processor
        self._lock = threading.Lock()

    def process_frame(self, frame: np.ndarray, frame_index: int) -> Tuple[int, np.ndarray]:
        """线程安全的帧处理方法"""
        try:
            # OpenCV在多线程环境下是线程安全的，但为了保险起见，我们仍然使用锁
            with self._lock:
                processed_frame = self.processor.process(frame)

            # 确保输出格式正确
            if len(processed_frame.shape) == 2:
                processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
            elif len(processed_frame.shape) == 3 and processed_frame.shape[2] == 4:
                processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_BGRA2BGR)

            return frame_index, processed_frame

        except Exception as e:
            logger.error(f"Thread frame {frame_index} processing error: {e}")
            # 出错时返回原始帧
            if len(frame.shape) == 2:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
            return frame_index, frame


class OpenCVFFmpegProcessor:
    """
    优化的OpenCV + FFmpeg视频处理器
    - OpenCV并行处理视频帧（多进程CPU）
    - FFmpeg处理音频合并（专业）
    - 智能性能配置和资源管理
    """

    def __init__(self, input_path: str, preserve_audio: bool = True, performance_config: PerformanceConfig = None):
        self.input_path = input_path
        self.preserve_audio = preserve_audio
        self.output_dir = 'output'
        self.performance_config = performance_config or PerformanceConfig()

        # 性能监控
        self.processing_start_time = None
        self.frames_processed = 0
        self.memory_usage_mb = 0

        os.makedirs(self.output_dir, exist_ok=True)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
    
    def get_video_info(self) -> Dict[str, Any]:
        """使用FFprobe获取视频信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', 
                '-show_format', '-show_streams', self.input_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)
            
            # 解析视频流信息
            video_stream = None
            audio_stream = None
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video' and video_stream is None:
                    video_stream = stream
                elif stream['codec_type'] == 'audio' and audio_stream is None:
                    audio_stream = stream
            
            if not video_stream:
                raise ValueError("No video stream found")
            
            duration = float(info['format']['duration'])
            fps = eval(video_stream['r_frame_rate'])  # 例如 "60/1"
            width = int(video_stream['width'])
            height = int(video_stream['height'])
            total_frames = int(duration * fps)
            
            return {
                'duration': duration,
                'fps': fps,
                'size': (width, height),
                'total_frames': total_frames,
                'has_audio': audio_stream is not None
            }
            
        except Exception as e:
            logger.error(f"Failed to get video info: {e}")
            raise
    
    def process_frames_with_opencv(
        self,
        opencv_processor,  # 可以是Callable或SerializableProcessor
        output_prefix: str,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
        batch_size: int = None
    ) -> Dict[str, Any]:
        """
        使用OpenCV并行处理视频帧（优化的多进程实现）
        保持OpenCV为核心处理引擎，通过智能并行化提升性能
        """
        # 开始性能监控
        self.processing_start_time = time.time()
        self.frames_processed = 0

        video_info = self.get_video_info()

        # 根据视频尺寸调整性能配置
        width, height = video_info['size']
        total_frames = video_info['total_frames']
        self.performance_config.adjust_for_video_size(width, height, total_frames)

        # 使用配置的批处理大小，除非明确指定
        if batch_size is None:
            batch_size = self.performance_config.batch_size

        logger.info(f"Processing {total_frames} frames ({width}x{height}) with "
                   f"{self.performance_config.max_workers} workers, batch_size={batch_size}")

        # 创建临时视频文件（无音频）- 使用UUID确保并发安全
        import uuid
        unique_id = str(uuid.uuid4())[:8]  # 使用UUID的前8位确保唯一性
        temp_video_path = os.path.join(
            tempfile.gettempdir(),
            f'temp_processed_{unique_id}_{int(time.time())}.mp4'
        )

        if progress_callback:
            progress_callback(5, 100, 'Starting optimized video processing...')

        # 使用OpenCV进行处理
        cap = cv2.VideoCapture(self.input_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {self.input_path}")

        # 获取视频属性
        fps = video_info['fps']
        
        # 先处理第一帧来确定输出尺寸
        ret, first_frame = cap.read()
        if not ret:
            cap.release()
            raise ValueError("Cannot read first frame from video")

        # 处理第一帧以确定输出尺寸
        # 确保使用正确的处理器接口
        if isinstance(opencv_processor, SerializableProcessor):
            processed_first_frame = opencv_processor.process(first_frame)
        else:
            processed_first_frame = opencv_processor(first_frame)

        # 确定输出尺寸
        if len(processed_first_frame.shape) == 3:
            # 彩色图像
            output_height, output_width = processed_first_frame.shape[:2]
        else:
            # 灰度图像，需要转换为3通道
            output_height, output_width = processed_first_frame.shape
            processed_first_frame = cv2.cvtColor(processed_first_frame, cv2.COLOR_GRAY2BGR)

        # 创建视频写入器，使用处理后的尺寸
        # 尝试使用H.264编码器（更兼容）
        fourcc = cv2.VideoWriter_fourcc(*'H264')
        out = cv2.VideoWriter(temp_video_path, fourcc, fps, (output_width, output_height))

        # 如果H264失败，尝试使用mp4v
        if not out.isOpened():
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(temp_video_path, fourcc, fps, (output_width, output_height))

        if not out.isOpened():
            cap.release()
            raise ValueError("Cannot create output video writer")

        try:
            # 写入第一帧
            out.write(processed_first_frame)
            processed_frames = 1
            frame_batch = []
            batch_count = 0
            last_progress_update = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_batch.append(frame)

                # 智能批量处理
                if len(frame_batch) >= batch_size:
                    batch_start_index = processed_frames

                    # 使用优化的并行批处理
                    processed_batch = self._process_frame_batch(
                        frame_batch, opencv_processor, batch_start_index
                    )

                    # 写入处理后的帧
                    for processed_frame in processed_batch:
                        # 确保处理后的帧是3通道的
                        if len(processed_frame.shape) == 2:
                            processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
                        out.write(processed_frame)

                    processed_frames += len(processed_batch)
                    self.frames_processed = processed_frames
                    frame_batch = []
                    batch_count += 1

                    # 优化的进度更新（减少频率）
                    if progress_callback and (batch_count % self.performance_config.progress_update_interval == 0
                                            or processed_frames - last_progress_update >= 100):
                        progress = 10 + int((processed_frames / total_frames) * 70)

                        # 计算处理速度
                        elapsed_time = time.time() - self.processing_start_time
                        fps_current = processed_frames / elapsed_time if elapsed_time > 0 else 0

                        progress_callback(progress, 100,
                                        f'Processed {processed_frames}/{total_frames} frames '
                                        f'({fps_current:.1f} fps, {self.memory_usage_mb:.1f}MB)')
                        last_progress_update = processed_frames

            # 处理剩余帧
            if frame_batch:
                batch_start_index = processed_frames
                processed_batch = self._process_frame_batch(
                    frame_batch, opencv_processor, batch_start_index
                )
                for processed_frame in processed_batch:
                    # 确保处理后的帧是3通道的
                    if len(processed_frame.shape) == 2:
                        processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
                    out.write(processed_frame)
                processed_frames += len(processed_batch)
                self.frames_processed = processed_frames
            
        finally:
            cap.release()
            out.release()
        
        if progress_callback:
            progress_callback(85, 100, 'Merging audio with FFmpeg...')
        
        # 创建最终输出文件 - 使用相同的unique_id确保一致性
        output_filename = f"{output_prefix}_{unique_id}_{int(time.time())}.mp4"
        output_path = os.path.join(self.output_dir, output_filename)
        
        # 使用FFmpeg合并音频
        if self.preserve_audio and video_info['has_audio']:
            self._merge_audio_with_ffmpeg(temp_video_path, output_path)
        else:
            # 不需要音频，直接移动文件
            import shutil
            shutil.move(temp_video_path, output_path)
        
        # 清理临时文件
        if os.path.exists(temp_video_path):
            os.remove(temp_video_path)
        
        # 计算性能统计
        total_processing_time = time.time() - self.processing_start_time
        avg_fps = processed_frames / total_processing_time if total_processing_time > 0 else 0

        if progress_callback:
            progress_callback(100, 100, f'Processing completed - {avg_fps:.1f} fps average')

        # 记录性能信息
        logger.info(f"Video processing completed: {processed_frames} frames in {total_processing_time:.2f}s "
                   f"({avg_fps:.1f} fps), peak memory: {self.memory_usage_mb:.1f}MB")

        return {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_path': self.input_path,
            'has_audio': video_info['has_audio'] and self.preserve_audio,
            'duration': video_info['duration'],
            'fps': video_info['fps'],
            'size': video_info['size'],
            'frames_processed': processed_frames,
            'optimization': 'opencv_ffmpeg_parallel_cpu',
            'performance_stats': {
                'processing_time_seconds': total_processing_time,
                'average_fps': avg_fps,
                'peak_memory_mb': self.memory_usage_mb,
                'workers_used': self.performance_config.max_workers,
                'batch_size': batch_size
            }
        }
    
    def _process_frame_batch(
        self,
        frame_batch: List[np.ndarray],
        opencv_processor: SerializableProcessor,
        batch_start_index: int = 0
    ) -> List[np.ndarray]:
        """
        智能并行批量处理帧（支持线程池和进程池）
        保持OpenCV为核心处理引擎，通过并行化提升性能
        """
        if not frame_batch:
            return []

        batch_size = len(frame_batch)
        pool_type = "ThreadPool" if self.performance_config.use_thread_pool else "ProcessPool"
        logger.debug(f"Processing batch of {batch_size} frames with {self.performance_config.max_workers} workers ({pool_type})")

        # 监控内存使用
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        try:
            if self.performance_config.use_thread_pool:
                # 使用线程池处理（Celery环境）
                return self._process_with_thread_pool(frame_batch, opencv_processor, batch_start_index)
            else:
                # 使用进程池处理（独立环境）
                return self._process_with_process_pool(frame_batch, opencv_processor, batch_start_index)

        except Exception as e:
            logger.error(f"Parallel batch processing failed: {e}")
            # Fallback到串行处理
            logger.warning("Falling back to serial processing")
            return self._process_frame_batch_serial(frame_batch, opencv_processor)

    def _process_with_thread_pool(
        self,
        frame_batch: List[np.ndarray],
        opencv_processor: SerializableProcessor,
        batch_start_index: int
    ) -> List[np.ndarray]:
        """使用线程池处理帧批次"""
        batch_size = len(frame_batch)

        # 创建线程安全的处理器
        thread_processor = ThreadSafeFrameProcessor(opencv_processor)

        # 使用ThreadPoolExecutor进行并行处理
        with ThreadPoolExecutor(max_workers=self.performance_config.max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(thread_processor.process_frame, frame, batch_start_index + i): i
                for i, frame in enumerate(frame_batch)
            }

            # 收集结果，保持顺序
            results = [None] * batch_size
            completed_count = 0

            for future in as_completed(future_to_index):
                try:
                    frame_index, processed_frame = future.result()
                    original_index = frame_index - batch_start_index
                    results[original_index] = processed_frame
                    completed_count += 1

                except Exception as e:
                    original_index = future_to_index[future]
                    logger.error(f"Thread processing error for frame {original_index}: {e}")
                    # 使用原始帧作为fallback
                    results[original_index] = frame_batch[original_index]
                    completed_count += 1

        # 确保所有帧都被处理
        for i, result in enumerate(results):
            if result is None:
                logger.warning(f"Frame {i} was not processed, using original")
                results[i] = frame_batch[i]

        return results

    def _process_with_process_pool(
        self,
        frame_batch: List[np.ndarray],
        opencv_processor: SerializableProcessor,
        batch_start_index: int
    ) -> List[np.ndarray]:
        """使用进程池处理帧批次"""
        batch_size = len(frame_batch)

        # 准备并行处理的数据
        frame_data_list = [
            (frame, opencv_processor, batch_start_index + i)
            for i, frame in enumerate(frame_batch)
        ]

        # 使用ProcessPoolExecutor进行并行处理
        with ProcessPoolExecutor(max_workers=self.performance_config.max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(process_frame_with_opencv, frame_data): i
                for i, frame_data in enumerate(frame_data_list)
            }

            # 收集结果，保持顺序
            results = [None] * batch_size
            completed_count = 0

            for future in as_completed(future_to_index):
                try:
                    frame_index, processed_frame = future.result()
                    original_index = frame_index - batch_start_index
                    results[original_index] = processed_frame
                    completed_count += 1

                except Exception as e:
                    original_index = future_to_index[future]
                    logger.error(f"Process processing error for frame {original_index}: {e}")
                    # 使用原始帧作为fallback
                    results[original_index] = frame_batch[original_index]
                    completed_count += 1

        # 确保所有帧都被处理
        for i, result in enumerate(results):
            if result is None:
                logger.warning(f"Frame {i} was not processed, using original")
                results[i] = frame_batch[i]

        return results

    def _process_frame_batch_serial(
        self,
        frame_batch: List[np.ndarray],
        opencv_processor: SerializableProcessor
    ) -> List[np.ndarray]:
        """串行处理帧（fallback方法）"""
        processed_batch = []

        for frame in frame_batch:
            try:
                processed_frame = opencv_processor.process(frame)

                # 确保输出格式正确
                if len(processed_frame.shape) == 2:
                    processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
                elif len(processed_frame.shape) == 3 and processed_frame.shape[2] == 4:
                    processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_BGRA2BGR)

                processed_batch.append(processed_frame)

            except Exception as e:
                logger.error(f"Frame processing error: {e}")
                processed_batch.append(frame)  # 出错时使用原始帧

        return processed_batch

    def _detect_hardware_acceleration(self) -> Optional[str]:
        """检测可用的硬件加速选项"""
        try:
            # 检测NVIDIA GPU (NVENC)
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                logger.info("NVIDIA GPU detected, using NVENC acceleration")
                return 'cuda'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        try:
            # 检测Intel Quick Sync
            result = subprocess.run(['ffmpeg', '-hwaccels'], capture_output=True, text=True, timeout=5)
            if 'qsv' in result.stdout:
                logger.info("Intel Quick Sync detected")
                return 'qsv'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        try:
            # 检测VAAPI (Linux)
            if os.path.exists('/dev/dri'):
                logger.info("VAAPI hardware acceleration available")
                return 'vaapi'
        except Exception:
            pass

        logger.debug("No hardware acceleration detected, using software encoding")
        return None

    def _merge_audio_with_ffmpeg(self, video_path: str, output_path: str):
        """使用优化的FFmpeg合并音频"""
        try:
            # 构建优化的FFmpeg命令
            cmd = [
                'ffmpeg', '-i', video_path, '-i', self.input_path,
                '-c:v', 'copy',  # 复制视频流，不重编码
                '-c:a', 'aac',   # 音频编码为AAC
                '-map', '0:v:0', # 使用第一个文件的视频流
                '-map', '1:a:0', # 使用第二个文件的音频流
                '-shortest',     # 以较短的流为准
                '-threads', str(self.performance_config.ffmpeg_threads),  # 使用多线程
                '-preset', 'fast',  # 快速预设
                '-y',            # 覆盖输出文件
                output_path
            ]

            # 如果启用硬件加速，尝试添加硬件加速选项
            if self.performance_config.enable_hardware_accel:
                # 检测可用的硬件加速
                hw_accel = self._detect_hardware_acceleration()
                if hw_accel:
                    cmd.insert(1, '-hwaccel')
                    cmd.insert(2, hw_accel)
            
            logger.info(f"FFmpeg merge command: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("Audio merging completed successfully")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg audio merging failed: {e.stderr}")
            # 如果音频合并失败，至少保留视频
            import shutil
            shutil.move(video_path, output_path)
            logger.warning("Using video without audio due to merge failure")
        except Exception as e:
            logger.error(f"Audio merging error: {e}")
            import shutil
            shutil.move(video_path, output_path)


def create_opencv_processor(operation_type: str, **parameters):
    """创建可序列化的OpenCV处理器"""
    return SerializableProcessor(operation_type, **parameters)
"""
OpenCV视频帧处理器模块
"""

import cv2
import numpy as np
from typing import Tuple


class SerializableProcessor:
    """可序列化的OpenCV处理器类"""

    def __init__(self, operation_type: str, **parameters):
        self.operation_type = operation_type
        self.parameters = parameters

    def process(self, frame: np.ndarray) -> np.ndarray:
        """处理单帧"""
        if self.operation_type == 'grayscale':
            return cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        elif self.operation_type == 'resize':
            width = self.parameters.get('width', 640)
            height = self.parameters.get('height', 480)
            return cv2.resize(frame, (width, height))

        elif self.operation_type == 'binary':
            threshold = self.parameters.get('threshold', 127)
            max_value = self.parameters.get('max_value', 255)
            threshold_type = self.parameters.get('threshold_type', 'binary')

            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            if threshold_type == 'binary':
                _, binary_frame = cv2.threshold(gray_frame, threshold, max_value, cv2.THRESH_BINARY)
            elif threshold_type == 'binary_inv':
                _, binary_frame = cv2.threshold(gray_frame, threshold, max_value, cv2.THRESH_BINARY_INV)
            else:
                _, binary_frame = cv2.threshold(gray_frame, threshold, max_value, cv2.THRESH_BINARY)

            return binary_frame

        elif self.operation_type == 'blur':
            filter_type = self.parameters.get('filter_type', 'gaussian')
            kernel_size = self.parameters.get('kernel_size', 15)
            sigma = self.parameters.get('sigma', 0)

            # 确保kernel_size为奇数
            if kernel_size % 2 == 0:
                kernel_size += 1

            if filter_type == 'gaussian':
                return cv2.GaussianBlur(frame, (kernel_size, kernel_size), sigma)
            elif filter_type == 'median':
                return cv2.medianBlur(frame, kernel_size)
            elif filter_type == 'bilateral':
                return cv2.bilateralFilter(frame, kernel_size, 75, 75)
            else:
                return cv2.GaussianBlur(frame, (kernel_size, kernel_size), sigma)

        elif self.operation_type == 'edge_detection':
            low_threshold = self.parameters.get('low_threshold', 50)
            high_threshold = self.parameters.get('high_threshold', 150)
            edge_type = self.parameters.get('edge_type', 'canny')

            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            if edge_type == 'canny':
                edges = cv2.Canny(gray_frame, low_threshold, high_threshold)
            elif edge_type == 'sobel':
                sobelx = cv2.Sobel(gray_frame, cv2.CV_64F, 1, 0, ksize=3)
                sobely = cv2.Sobel(gray_frame, cv2.CV_64F, 0, 1, ksize=3)
                edges = np.sqrt(sobelx**2 + sobely**2)
                edges = np.uint8(edges / edges.max() * 255)
            elif edge_type == 'laplacian':
                edges = cv2.Laplacian(gray_frame, cv2.CV_64F)
                edges = np.uint8(np.absolute(edges))
            else:
                edges = gray_frame

            return edges

        elif self.operation_type == 'transform':
            transform_type = self.parameters.get('transform_type', 'rotate_90')
            angle = self.parameters.get('angle', 90)

            height, width = frame.shape[:2]

            if transform_type == 'rotate_90':
                return cv2.rotate(frame, cv2.ROTATE_90_CLOCKWISE)
            elif transform_type == 'rotate_180':
                return cv2.rotate(frame, cv2.ROTATE_180)
            elif transform_type == 'rotate_270':
                return cv2.rotate(frame, cv2.ROTATE_90_COUNTERCLOCKWISE)
            elif transform_type == 'flip_horizontal':
                return cv2.flip(frame, 1)
            elif transform_type == 'flip_vertical':
                return cv2.flip(frame, 0)
            elif transform_type == 'flip_both':
                return cv2.flip(frame, -1)
            elif transform_type == 'rotate_custom':
                center = (width // 2, height // 2)
                rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
                return cv2.warpAffine(frame, rotation_matrix, (width, height))
            else:
                return cv2.rotate(frame, cv2.ROTATE_90_CLOCKWISE)

        else:
            # 默认返回原始帧
            return frame


def process_frame_with_opencv(frame_data: Tuple[np.ndarray, SerializableProcessor, int]) -> Tuple[int, np.ndarray]:
    """处理单帧的全局函数（用于多进程）"""
    frame, processor, frame_index = frame_data
    try:
        processed_frame = processor.process(frame)
        return frame_index, processed_frame
    except Exception as e:
        # 返回原始帧作为fallback
        return frame_index, frame


def create_opencv_processor(operation_type: str, **parameters):
    """创建可序列化的OpenCV处理器"""
    return SerializableProcessor(operation_type, **parameters)

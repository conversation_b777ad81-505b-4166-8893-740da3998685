"""
FFmpeg视频处理器模块
"""

import cv2
import subprocess
import os
import time
import tempfile
import json
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Callable, Optional, Dict, Any, List, Tuple
import logging
import numpy as np
import threading

from .performance_config import PerformanceConfig
from .opencv_processors import SerializableProcessor, process_frame_with_opencv

logger = logging.getLogger(__name__)


class OpenCVFFmpegProcessor:
    """优化的OpenCV + FFmpeg视频处理器"""

    def __init__(self, input_path: str, preserve_audio: bool = True):
        self.input_path = input_path
        self.preserve_audio = preserve_audio
        self.performance_config = PerformanceConfig()
        
        # 视频信息
        self.cap = None
        self.total_frames = 0
        self.fps = 0
        self.width = 0
        self.height = 0
        
        # 临时文件管理
        self.temp_dir = None
        self.temp_files = []
        
        # 初始化视频信息
        self._initialize_video_info()

    def __enter__(self):
        """上下文管理器入口"""
        self.temp_dir = tempfile.mkdtemp(prefix='opencv_ffmpeg_')
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理资源"""
        self._cleanup()

    def _initialize_video_info(self):
        """初始化视频信息"""
        try:
            self.cap = cv2.VideoCapture(self.input_path)
            if not self.cap.isOpened():
                raise ValueError(f"Cannot open video file: {self.input_path}")
            
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 根据视频尺寸调整性能配置
            self.performance_config.adjust_for_video_size(self.width, self.height, self.total_frames)
            
            logger.info(f"Video info: {self.width}x{self.height}, {self.total_frames} frames, {self.fps:.2f} fps")
            
        except Exception as e:
            logger.error(f"Failed to initialize video info: {e}")
            raise

    def _cleanup(self):
        """清理资源"""
        try:
            if self.cap:
                self.cap.release()
            
            # 清理临时文件
            for temp_file in self.temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except Exception as e:
                    logger.warning(f"Failed to remove temp file {temp_file}: {e}")
            
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    os.rmdir(self.temp_dir)
                except Exception as e:
                    logger.warning(f"Failed to remove temp directory {self.temp_dir}: {e}")
                    
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

    def _read_frames_batch(self, start_frame: int, batch_size: int) -> List[Tuple[int, np.ndarray]]:
        """批量读取帧"""
        frames = []
        
        # 设置起始帧位置
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        for i in range(batch_size):
            ret, frame = self.cap.read()
            if not ret:
                break
            frames.append((start_frame + i, frame))
        
        return frames

    def _process_frames_batch(self, frames_batch: List[Tuple[int, np.ndarray]], 
                            opencv_processor: SerializableProcessor) -> List[Tuple[int, np.ndarray]]:
        """批量处理帧"""
        if not frames_batch:
            return []
        
        # 准备处理数据
        frame_data = [(frame, opencv_processor, frame_idx) for frame_idx, frame in frames_batch]
        
        # 选择执行器类型
        executor_class = ThreadPoolExecutor if self.performance_config.use_thread_pool else ProcessPoolExecutor
        
        processed_frames = []
        with executor_class(max_workers=self.performance_config.max_workers) as executor:
            # 提交所有任务
            future_to_frame = {
                executor.submit(process_frame_with_opencv, data): data[2] 
                for data in frame_data
            }
            
            # 收集结果
            for future in as_completed(future_to_frame):
                try:
                    frame_idx, processed_frame = future.result()
                    processed_frames.append((frame_idx, processed_frame))
                except Exception as e:
                    frame_idx = future_to_frame[future]
                    logger.error(f"Frame {frame_idx} processing failed: {e}")
                    # 使用原始帧作为fallback
                    original_frame = next(frame for idx, frame in frames_batch if idx == frame_idx)
                    processed_frames.append((frame_idx, original_frame))
        
        # 按帧索引排序
        processed_frames.sort(key=lambda x: x[0])
        return processed_frames

    def _write_frames_to_video(self, processed_frames: List[Tuple[int, np.ndarray]],
                              output_path: str) -> str:
        """将处理后的帧写入视频文件"""
        if not processed_frames:
            raise ValueError("No frames to write")

        # 获取第一帧来确定输出格式
        first_frame = processed_frames[0][1]

        # 确定输出尺寸和格式
        if len(first_frame.shape) == 2:  # 灰度图
            output_height, output_width = first_frame.shape
            is_color = False
        else:  # 彩色图
            output_height, output_width = first_frame.shape[:2]
            is_color = True

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, self.fps, (output_width, output_height), is_color)

        try:
            for frame_idx, frame in processed_frames:
                # 确保帧格式正确
                if is_color and len(frame.shape) == 2:
                    # 灰度转彩色
                    frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
                elif not is_color and len(frame.shape) == 3:
                    # 彩色转灰度
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                out.write(frame)
        finally:
            out.release()

        return output_path

    def process_frames_with_opencv(self, opencv_processor: SerializableProcessor,
                                 output_prefix: str = 'processed',
                                 progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """使用OpenCV处理所有帧"""
        try:
            # 生成输出文件名
            timestamp = int(time.time())
            temp_video_path = os.path.join(self.temp_dir, f'{output_prefix}_{timestamp}.mp4')

            all_processed_frames = []
            processed_count = 0

            # 分批处理帧
            for start_frame in range(0, self.total_frames, self.performance_config.batch_size):
                batch_size = min(self.performance_config.batch_size, self.total_frames - start_frame)

                # 读取帧批次
                frames_batch = self._read_frames_batch(start_frame, batch_size)
                if not frames_batch:
                    break

                # 处理帧批次
                processed_batch = self._process_frames_batch(frames_batch, opencv_processor)
                all_processed_frames.extend(processed_batch)

                processed_count += len(processed_batch)

                # 更新进度
                if progress_callback and processed_count % self.performance_config.progress_update_interval == 0:
                    progress = int((processed_count / self.total_frames) * 80)  # 80%用于帧处理
                    progress_callback(progress, 100, f"Processing frames: {processed_count}/{self.total_frames}")

            # 写入视频文件
            if progress_callback:
                progress_callback(85, 100, "Writing video file...")

            self._write_frames_to_video(all_processed_frames, temp_video_path)
            self.temp_files.append(temp_video_path)

            # 处理音频合并
            output_dir = 'output'
            os.makedirs(output_dir, exist_ok=True)

            if self.preserve_audio:
                if progress_callback:
                    progress_callback(90, 100, "Merging audio...")

                final_output_path = os.path.join(output_dir, f'{output_prefix}_{timestamp}.mp4')
                self._merge_audio_with_ffmpeg(temp_video_path, final_output_path)
            else:
                # 直接移动视频文件
                final_output_path = os.path.join(output_dir, f'{output_prefix}_{timestamp}.mp4')
                import shutil
                shutil.move(temp_video_path, final_output_path)

            if progress_callback:
                progress_callback(100, 100, "Processing completed")

            return {
                'status': 'success',
                'output_path': final_output_path,
                'output_filename': os.path.basename(final_output_path),
                'original_path': self.input_path,
                'frames_processed': processed_count,
                'processing_time': time.time() - timestamp
            }

        except Exception as e:
            logger.error(f"Frame processing failed: {e}")
            raise

    def _detect_hardware_acceleration(self) -> Optional[str]:
        """检测可用的硬件加速"""
        try:
            # 检测NVIDIA GPU
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                logger.info("NVIDIA GPU detected")
                return 'cuda'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        try:
            # 检测Intel Quick Sync
            result = subprocess.run(['ffmpeg', '-hwaccels'], capture_output=True, text=True, timeout=5)
            if 'qsv' in result.stdout:
                logger.info("Intel Quick Sync detected")
                return 'qsv'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        try:
            # 检测VAAPI (Linux)
            if os.path.exists('/dev/dri'):
                logger.info("VAAPI hardware acceleration available")
                return 'vaapi'
        except Exception:
            pass

        logger.debug("No hardware acceleration detected, using software encoding")
        return None

    def _merge_audio_with_ffmpeg(self, video_path: str, output_path: str):
        """使用优化的FFmpeg合并音频"""
        try:
            # 构建优化的FFmpeg命令
            cmd = [
                'ffmpeg', '-i', video_path, '-i', self.input_path,
                '-c:v', 'copy',  # 复制视频流，不重编码
                '-c:a', 'aac',   # 音频编码为AAC
                '-map', '0:v:0', # 使用第一个文件的视频流
                '-map', '1:a:0', # 使用第二个文件的音频流
                '-shortest',     # 以较短的流为准
                '-threads', str(self.performance_config.ffmpeg_threads),  # 使用多线程
                '-preset', 'fast',  # 快速预设
                '-y',            # 覆盖输出文件
                output_path
            ]

            # 如果启用硬件加速，尝试添加硬件加速选项
            if self.performance_config.enable_hardware_accel:
                # 检测可用的硬件加速
                hw_accel = self._detect_hardware_acceleration()
                if hw_accel:
                    cmd.insert(1, '-hwaccel')
                    cmd.insert(2, hw_accel)

            logger.info(f"FFmpeg merge command: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("Audio merging completed successfully")

        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg audio merging failed: {e.stderr}")
            # 如果音频合并失败，至少保留视频
            import shutil
            shutil.move(video_path, output_path)
            logger.warning("Using video without audio due to merge failure")
        except Exception as e:
            logger.error(f"Audio merging error: {e}")
            import shutil
            shutil.move(video_path, output_path)

"""
视频缩略图生成任务
基于现有video_extract_frame.py，专门用于生成视频第一帧缩略图
"""
import os
import cv2
import logging
import hashlib
from celery import current_task
from config import THUMBNAIL_FOLDER
from utils.cache_manager import thumbnail_cache

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def generate_thumbnail_filename(file_path, size=(320, 240)):
    """
    生成缩略图文件名，基于原文件路径和尺寸的哈希
    确保相同文件和尺寸生成相同的缩略图文件名
    """
    # 使用文件路径和尺寸生成唯一标识
    content = f"{file_path}_{size[0]}x{size[1]}"
    hash_obj = hashlib.md5(content.encode())
    hash_hex = hash_obj.hexdigest()
    
    # 获取原文件名（不含扩展名）
    base_name = os.path.splitext(os.path.basename(file_path))[0]
    
    # 生成缩略图文件名
    thumbnail_filename = f"thumb_{base_name}_{hash_hex[:8]}_{size[0]}x{size[1]}.jpg"
    return thumbnail_filename


def check_thumbnail_exists(file_path, size=(320, 240), frame_number=0):
    """
    检查缩略图是否已存在（使用缓存管理器）
    返回 (exists, thumbnail_path)
    """
    cached_path = thumbnail_cache.get_cached_thumbnail(file_path, size, frame_number)
    if cached_path:
        return True, cached_path

    # 兼容旧的文件名格式
    thumbnail_filename = generate_thumbnail_filename(file_path, size)
    thumbnail_path = os.path.join(THUMBNAIL_FOLDER, thumbnail_filename)

    if os.path.exists(thumbnail_path):
        return True, thumbnail_path
    return False, thumbnail_path


def generate_video_thumbnail_task(self, file_path, parameters=None):
    """
    生成视频缩略图任务
    
    Args:
        file_path: 视频文件路径
        parameters: 参数字典，可包含:
            - size: 缩略图尺寸，默认(320, 240)
            - frame_number: 提取的帧号，默认0（第一帧）
            - quality: JPEG质量，默认85
    
    Returns:
        dict: 包含缩略图信息的结果字典
    """
    try:
        progress_callback = create_celery_progress_callback(self)
        
        # 解析参数
        if parameters is None:
            parameters = {}
        
        size = parameters.get('size', (320, 240))
        frame_number = parameters.get('frame_number', 0)
        quality = parameters.get('quality', 85)
        
        progress_callback(5, 100, 'Checking cache...')

        # 检查缓存
        exists, thumbnail_path = check_thumbnail_exists(file_path, size, frame_number)
        if exists:
            progress_callback(100, 100, 'Thumbnail found in cache')

            result = {
                'status': 'success',
                'thumbnail_path': thumbnail_path,
                'thumbnail_filename': os.path.basename(thumbnail_path),
                'original_path': file_path,
                'size': size,
                'frame_number': frame_number,
                'cached': True
            }

            self.update_state(state='SUCCESS', meta=result)
            return result
        
        progress_callback(10, 100, 'Opening video file...')
        
        # 验证文件存在
        if not os.path.exists(file_path):
            raise ValueError(f"Video file not found: {file_path}")
        
        # 使用OpenCV打开视频
        cap = cv2.VideoCapture(file_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {file_path}")
        
        try:
            # 获取视频信息
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            if frame_number >= total_frames:
                logger.warning(f"Frame number {frame_number} exceeds total frames {total_frames}, using frame 0")
                frame_number = 0
            
            progress_callback(30, 100, f'Seeking to frame {frame_number}...')
            
            # 跳转到指定帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            progress_callback(50, 100, 'Extracting frame...')
            
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                raise ValueError(f"Cannot read frame {frame_number}")
            
            progress_callback(70, 100, 'Resizing thumbnail...')
            
            # 调整尺寸
            if (width, height) != size:
                frame = cv2.resize(frame, size, interpolation=cv2.INTER_AREA)
            
            progress_callback(85, 100, 'Saving thumbnail...')
            
            # 确保缩略图目录存在
            os.makedirs(THUMBNAIL_FOLDER, exist_ok=True)

            # 生成新的缓存文件名
            cache_key = thumbnail_cache.generate_cache_key(file_path, size, frame_number)
            cache_filename = thumbnail_cache.get_cache_filename(cache_key, file_path, size)
            final_thumbnail_path = os.path.join(THUMBNAIL_FOLDER, cache_filename)

            # 保存缩略图
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            success = cv2.imwrite(final_thumbnail_path, frame, encode_params)

            if not success:
                raise ValueError(f"Failed to save thumbnail: {final_thumbnail_path}")

            # 更新缓存
            thumbnail_cache.cache_thumbnail(file_path, final_thumbnail_path, size, frame_number)

            progress_callback(100, 100, 'Thumbnail generation completed')

            result = {
                'status': 'success',
                'thumbnail_path': final_thumbnail_path,
                'thumbnail_filename': os.path.basename(final_thumbnail_path),
                'original_path': file_path,
                'size': size,
                'frame_number': frame_number,
                'total_frames': total_frames,
                'fps': fps,
                'original_size': (width, height),
                'cached': False
            }

            self.update_state(state='SUCCESS', meta=result)
            return result
            
        finally:
            cap.release()
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Video thumbnail generation failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video thumbnail generation failed: {error_msg}")


def cleanup_old_thumbnails(max_age_days=7, max_unused_days=3):
    """
    清理过期的缩略图文件（使用缓存管理器）

    Args:
        max_age_days: 最大保留天数，默认7天
        max_unused_days: 最大未使用天数，默认3天
    """
    try:
        stats = thumbnail_cache.cleanup_expired_cache(max_age_days, max_unused_days)
        logger.info(f"Thumbnail cleanup completed: {stats}")
        return stats
    except Exception as e:
        logger.error(f"Thumbnail cleanup failed: {e}")
        return {'error': str(e)}

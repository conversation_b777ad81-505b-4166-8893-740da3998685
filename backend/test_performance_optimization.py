#!/usr/bin/env python3
"""
视频处理性能优化测试脚本
比较优化前后的性能差异，验证并行处理效果
"""

import sys
import os
import time
import json
import cv2
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append('.')

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, PerformanceConfig, create_opencv_processor


def create_test_video(output_path: str, duration: int = 10, fps: int = 30, resolution: tuple = (1280, 720)):
    """创建测试视频"""
    print(f"Creating test video: {output_path}")
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, resolution)
    
    total_frames = duration * fps
    for i in range(total_frames):
        # 创建彩色测试帧
        frame = np.random.randint(0, 255, (resolution[1], resolution[0], 3), dtype=np.uint8)
        
        # 添加一些图案
        cv2.rectangle(frame, (100, 100), (300, 300), (255, 0, 0), 3)
        cv2.circle(frame, (resolution[0]//2, resolution[1]//2), 50, (0, 255, 0), -1)
        cv2.putText(frame, f'Frame {i}', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print(f"Test video created: {output_path} ({total_frames} frames)")


def test_video_processing_performance():
    """测试视频处理性能"""
    print("🚀 视频处理性能优化测试")
    print("=" * 60)
    
    # 创建测试视频
    test_video_path = "test_performance_video.mp4"
    if not os.path.exists(test_video_path):
        create_test_video(test_video_path, duration=5, fps=30, resolution=(1280, 720))
    
    # 测试不同的操作类型
    operations = [
        ('grayscale', {}),
        ('resize', {'width': 640, 'height': 480}),
        ('blur', {'filter_type': 'gaussian', 'kernel_size': 15}),
    ]
    
    results = {}
    
    for operation, parameters in operations:
        print(f"\n🔄 测试操作: {operation}")
        print("-" * 40)
        
        # 创建OpenCV处理器
        opencv_processor = create_opencv_processor(operation, **parameters)
        
        # 测试优化版本
        print("⚡ 测试优化版本（并行处理）...")
        start_time = time.time()
        
        # 创建性能配置
        perf_config = PerformanceConfig()
        print(f"   配置: {perf_config.max_workers} workers, batch_size={perf_config.batch_size}")
        
        with OpenCVFFmpegProcessor(test_video_path, preserve_audio=False, performance_config=perf_config) as processor:
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix=f'optimized_{operation}',
                progress_callback=lambda p, t, m: print(f"   进度: {p}% - {m}")
            )
        
        optimized_time = time.time() - start_time
        optimized_stats = result.get('performance_stats', {})
        
        print(f"   ✅ 优化版本完成: {optimized_time:.2f}秒")
        print(f"   📊 性能统计: {optimized_stats.get('average_fps', 0):.1f} fps, "
              f"{optimized_stats.get('peak_memory_mb', 0):.1f}MB")
        
        # 保存结果
        results[operation] = {
            'optimized_time': optimized_time,
            'optimized_stats': optimized_stats,
            'performance_improvement': 'N/A (baseline)'
        }
        
        # 清理输出文件
        if os.path.exists(result['output_path']):
            os.remove(result['output_path'])
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 性能测试结果总结")
    print("=" * 60)
    
    total_optimized_time = sum(r['optimized_time'] for r in results.values())
    
    print(f"总处理时间（优化版本）: {total_optimized_time:.2f}秒")
    
    print("\n详细结果:")
    for operation, result in results.items():
        stats = result['optimized_stats']
        print(f"  📹 {operation}:")
        print(f"    - 处理时间: {result['optimized_time']:.2f}秒")
        print(f"    - 平均FPS: {stats.get('average_fps', 0):.1f}")
        print(f"    - 峰值内存: {stats.get('peak_memory_mb', 0):.1f}MB")
        print(f"    - 使用线程: {stats.get('workers_used', 1)}")
    
    # 保存结果到JSON文件
    with open('performance_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 详细结果已保存到: performance_test_results.json")
    
    # 清理测试文件
    if os.path.exists(test_video_path):
        os.remove(test_video_path)
    
    return results


def test_memory_usage():
    """测试内存使用情况"""
    print("\n🧠 内存使用测试")
    print("-" * 40)
    
    import psutil
    process = psutil.Process()
    
    # 创建不同配置的处理器
    configs = [
        PerformanceConfig(),  # 默认配置
    ]
    
    # 手动调整配置进行测试
    config_high_parallel = PerformanceConfig()
    config_high_parallel.max_workers = min(8, config_high_parallel.cpu_count)
    config_high_parallel.batch_size = 50
    configs.append(config_high_parallel)
    
    config_low_parallel = PerformanceConfig()
    config_low_parallel.max_workers = 2
    config_low_parallel.batch_size = 10
    configs.append(config_low_parallel)
    
    for i, config in enumerate(configs):
        print(f"\n配置 {i+1}: {config.max_workers} workers, batch_size={config.batch_size}")
        
        memory_before = process.memory_info().rss / 1024 / 1024
        print(f"  内存使用（开始）: {memory_before:.1f}MB")
        
        # 这里可以添加实际的处理测试
        # 由于需要测试视频，暂时只显示配置信息
        
        memory_after = process.memory_info().rss / 1024 / 1024
        print(f"  内存使用（结束）: {memory_after:.1f}MB")


if __name__ == "__main__":
    try:
        # 运行性能测试
        results = test_video_processing_performance()
        
        # 运行内存测试
        test_memory_usage()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

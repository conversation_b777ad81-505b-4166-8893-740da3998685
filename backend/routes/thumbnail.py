"""
视频缩略图API路由
"""
import os
import logging
from flask import Blueprint, send_file, abort, request, jsonify
from config import UPLOAD_FOLDER, THUMBNAIL_FOLDER
from utils.validation import validate_file_path, allowed_file
from tasks.video_thumbnail import check_thumbnail_exists, generate_thumbnail_filename

logger = logging.getLogger(__name__)

thumbnail_bp = Blueprint('thumbnail', __name__)


@thumbnail_bp.route('/api/thumbnail/<filename>', methods=['GET'])
def get_video_thumbnail(filename):
    """
    获取视频缩略图
    如果缩略图不存在，则异步生成并返回生成状态
    如果缩略图存在，直接返回缩略图文件
    
    Query Parameters:
        - size: 缩略图尺寸，格式为 "widthxheight"，默认 "320x240"
        - sync: 是否同步生成，默认false（异步）
    """
    try:
        # 解析尺寸参数
        size_param = request.args.get('size', '320x240')
        sync = request.args.get('sync', 'false').lower() == 'true'
        
        try:
            width, height = map(int, size_param.split('x'))
            size = (width, height)
            
            # 限制尺寸范围
            if width < 50 or width > 1920 or height < 50 or height > 1080:
                return jsonify({'error': 'Invalid size range. Width and height must be between 50 and 1920/1080'}), 400
                
        except ValueError:
            return jsonify({'error': 'Invalid size format. Use "widthxheight" format (e.g., "320x240")'}), 400
        
        # 构造原视频文件路径
        video_path = os.path.join(UPLOAD_FOLDER, filename)
        video_path = os.path.abspath(video_path)
        
        # 验证文件路径安全性
        abs_upload_folder = os.path.abspath(UPLOAD_FOLDER)
        if not validate_file_path(video_path, abs_upload_folder):
            abort(403)  # Forbidden
        
        # 检查原视频文件是否存在
        if not os.path.exists(video_path):
            return jsonify({'error': 'Video file not found'}), 404
        
        # 验证是否为视频文件
        if not allowed_file(filename, 'video'):
            return jsonify({'error': 'File is not a supported video format'}), 400
        
        # 检查缩略图是否已存在
        exists, thumbnail_path = check_thumbnail_exists(video_path, size)
        
        if exists:
            # 缩略图存在，直接返回
            try:
                return send_file(thumbnail_path, mimetype='image/jpeg')
            except Exception as e:
                logger.error(f"Failed to send thumbnail file {thumbnail_path}: {e}")
                return jsonify({'error': 'Failed to serve thumbnail'}), 500
        
        # 缩略图不存在，需要生成
        if sync:
            # 同步生成（阻塞）
            return _generate_thumbnail_sync(video_path, size)
        else:
            # 异步生成（推荐）
            return _generate_thumbnail_async(video_path, size, filename)
            
    except Exception as e:
        logger.error(f"Thumbnail request error: {e}")
        return jsonify({'error': 'Internal server error'}), 500


def _generate_thumbnail_sync(video_path, size):
    """同步生成缩略图"""
    try:
        from app import tasks
        
        if not tasks:
            return jsonify({'error': 'Task system not available'}), 503
        
        # 同步调用任务
        task = tasks['generate_video_thumbnail']
        result = task.apply(args=[video_path, {'size': size}])
        
        if result.successful():
            task_result = result.result
            thumbnail_path = task_result['thumbnail_path']
            
            if os.path.exists(thumbnail_path):
                return send_file(thumbnail_path, mimetype='image/jpeg')
            else:
                return jsonify({'error': 'Thumbnail generation failed'}), 500
        else:
            error_msg = str(result.result) if result.result else 'Unknown error'
            return jsonify({'error': f'Thumbnail generation failed: {error_msg}'}), 500
            
    except Exception as e:
        logger.error(f"Sync thumbnail generation error: {e}")
        return jsonify({'error': 'Thumbnail generation failed'}), 500


def _generate_thumbnail_async(video_path, size, filename):
    """异步生成缩略图"""
    try:
        from app import tasks
        
        if not tasks:
            return jsonify({'error': 'Task system not available'}), 503
        
        # 异步调用任务
        task = tasks['generate_video_thumbnail'].delay(video_path, {'size': size})
        
        # 返回任务状态和预期的缩略图文件名
        thumbnail_filename = generate_thumbnail_filename(video_path, size)
        
        return jsonify({
            'status': 'generating',
            'task_id': task.id,
            'thumbnail_filename': thumbnail_filename,
            'thumbnail_url': f'/api/thumbnail/{filename}?size={size[0]}x{size[1]}',
            'status_url': f'/api/thumbnail/status/{task.id}',
            'message': 'Thumbnail generation started'
        }), 202  # Accepted
        
    except Exception as e:
        logger.error(f"Async thumbnail generation error: {e}")
        return jsonify({'error': 'Failed to start thumbnail generation'}), 500


@thumbnail_bp.route('/api/thumbnail/status/<task_id>', methods=['GET'])
def get_thumbnail_status(task_id):
    """
    查询缩略图生成任务状态
    """
    try:
        from app import celery
        
        # 获取任务状态
        task = celery.AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {
                'status': 'pending',
                'message': 'Task is waiting to be processed'
            }
        elif task.state == 'PROGRESS':
            response = {
                'status': 'progress',
                'current': task.info.get('current', 0),
                'total': task.info.get('total', 100),
                'message': task.info.get('status', 'Processing...')
            }
        elif task.state == 'SUCCESS':
            result = task.result
            response = {
                'status': 'success',
                'thumbnail_path': result.get('thumbnail_path'),
                'thumbnail_filename': result.get('thumbnail_filename'),
                'size': result.get('size'),
                'cached': result.get('cached', False),
                'message': 'Thumbnail generated successfully'
            }
        else:  # FAILURE
            response = {
                'status': 'failed',
                'error': str(task.info),
                'message': 'Thumbnail generation failed'
            }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Thumbnail status query error: {e}")
        return jsonify({'error': 'Failed to query task status'}), 500


@thumbnail_bp.route('/api/thumbnail/cleanup', methods=['POST'])
def cleanup_thumbnails():
    """
    清理过期缩略图
    需要管理员权限（这里简化处理）
    """
    try:
        # 获取参数
        data = request.get_json() or {}
        max_age_days = data.get('max_age_days', 7)
        
        if not isinstance(max_age_days, (int, float)) or max_age_days < 1:
            return jsonify({'error': 'Invalid max_age_days parameter'}), 400
        
        from tasks.video_thumbnail import cleanup_old_thumbnails
        cleanup_old_thumbnails(max_age_days)
        
        return jsonify({
            'status': 'success',
            'message': f'Cleanup completed for thumbnails older than {max_age_days} days'
        })
        
    except Exception as e:
        logger.error(f"Thumbnail cleanup error: {e}")
        return jsonify({'error': 'Cleanup failed'}), 500


@thumbnail_bp.route('/api/thumbnail/info/<filename>', methods=['GET'])
def get_thumbnail_info(filename):
    """
    获取缩略图信息（不生成缩略图）
    """
    try:
        # 解析尺寸参数
        size_param = request.args.get('size', '320x240')
        
        try:
            width, height = map(int, size_param.split('x'))
            size = (width, height)
        except ValueError:
            return jsonify({'error': 'Invalid size format'}), 400
        
        # 构造原视频文件路径
        video_path = os.path.join(UPLOAD_FOLDER, filename)
        video_path = os.path.abspath(video_path)
        
        # 验证文件路径安全性
        abs_upload_folder = os.path.abspath(UPLOAD_FOLDER)
        if not validate_file_path(video_path, abs_upload_folder):
            abort(403)
        
        # 检查原视频文件是否存在
        if not os.path.exists(video_path):
            return jsonify({'error': 'Video file not found'}), 404
        
        # 检查缩略图状态
        exists, thumbnail_path = check_thumbnail_exists(video_path, size)
        thumbnail_filename = generate_thumbnail_filename(video_path, size)
        
        return jsonify({
            'video_filename': filename,
            'thumbnail_exists': exists,
            'thumbnail_filename': thumbnail_filename,
            'thumbnail_path': thumbnail_path if exists else None,
            'size': size,
            'thumbnail_url': f'/api/thumbnail/{filename}?size={size[0]}x{size[1]}'
        })
        
    except Exception as e:
        logger.error(f"Thumbnail info error: {e}")
        return jsonify({'error': 'Failed to get thumbnail info'}), 500

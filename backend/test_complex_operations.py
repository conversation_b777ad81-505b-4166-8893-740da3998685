#!/usr/bin/env python3
"""
复杂视频操作性能测试
测试不同操作类型的并行处理效果
"""

import sys
import os
import time
import cv2
import numpy as np

# 添加项目路径
sys.path.append('.')

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, PerformanceConfig, create_opencv_processor


def create_complex_test_video(output_path: str, frames: int = 150):
    """创建更复杂的测试视频"""
    print(f"Creating complex test video with {frames} frames...")
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, 30, (1280, 720))
    
    for i in range(frames):
        # 创建复杂的测试帧
        frame = np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8)
        
        # 添加几何图形
        cv2.rectangle(frame, (100, 100), (400, 400), (255, 0, 0), 3)
        cv2.circle(frame, (640, 360), 100, (0, 255, 0), -1)
        cv2.ellipse(frame, (900, 200), (150, 75), 45, 0, 360, (0, 0, 255), 5)
        
        # 添加文字
        cv2.putText(frame, f'Frame {i}', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(frame, f'Complex Test', (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        out.write(frame)
    
    out.release()
    print(f"Complex test video created: {output_path}")


def test_multiple_operations():
    """测试多种操作的性能"""
    print("🚀 复杂视频操作性能测试")
    print("=" * 60)
    
    # 创建测试视频
    test_video = "complex_test.mp4"
    if not os.path.exists(test_video):
        create_complex_test_video(test_video, frames=150)  # 5秒视频，1280x720
    
    # 测试不同操作
    operations = [
        ('grayscale', {}, '灰度化'),
        ('resize', {'width': 640, 'height': 480}, '缩放'),
        ('blur', {'filter_type': 'gaussian', 'kernel_size': 15}, '高斯模糊'),
        ('blur', {'filter_type': 'median', 'kernel_size': 9}, '中值滤波'),
        ('binary', {'threshold': 127, 'threshold_type': 'binary'}, '二值化'),
        ('edge_detection', {'edge_type': 'canny', 'low_threshold': 50, 'high_threshold': 150}, '边缘检测'),
        ('transform', {'transform_type': 'rotate_90'}, '旋转90度'),
    ]
    
    results = {}
    
    for operation, parameters, description in operations:
        print(f"\n🔄 测试操作: {description} ({operation})")
        print("-" * 50)
        
        # 创建OpenCV处理器
        opencv_processor = create_opencv_processor(operation, **parameters)
        
        # 创建性能配置
        perf_config = PerformanceConfig()
        print(f"   配置: {perf_config.max_workers} workers, batch_size={perf_config.batch_size}")
        
        # 进度回调
        def progress_callback(progress, total, message):
            if progress % 20 == 0 or progress >= 95:  # 减少输出频率
                print(f"   进度: {progress}% - {message}")
        
        # 测试处理
        start_time = time.time()
        
        try:
            with OpenCVFFmpegProcessor(test_video, preserve_audio=False, performance_config=perf_config) as processor:
                result = processor.process_frames_with_opencv(
                    opencv_processor=opencv_processor,
                    output_prefix=f'test_{operation}',
                    progress_callback=progress_callback
                )
            
            processing_time = time.time() - start_time
            
            print(f"   ✅ 完成: {processing_time:.2f}秒")
            
            # 显示性能统计
            if 'performance_stats' in result:
                stats = result['performance_stats']
                print(f"   📊 统计: {stats.get('average_fps', 0):.1f} fps, "
                      f"{stats.get('peak_memory_mb', 0):.1f}MB")
            
            # 保存结果
            results[operation] = {
                'description': description,
                'processing_time': processing_time,
                'stats': result.get('performance_stats', {}),
                'output_size_mb': os.path.getsize(result['output_path']) / 1024 / 1024 if os.path.exists(result['output_path']) else 0
            }
            
            # 清理输出文件
            if os.path.exists(result['output_path']):
                os.remove(result['output_path'])
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            results[operation] = {'error': str(e)}
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 复杂操作性能测试结果总结")
    print("=" * 60)
    
    total_time = sum(r.get('processing_time', 0) for r in results.values() if 'processing_time' in r)
    successful_operations = len([r for r in results.values() if 'processing_time' in r])
    
    print(f"成功操作: {successful_operations}/{len(operations)}")
    print(f"总处理时间: {total_time:.2f}秒")
    print(f"平均每操作: {total_time/successful_operations:.2f}秒" if successful_operations > 0 else "N/A")
    
    print("\n详细结果:")
    for operation, result in results.items():
        if 'processing_time' in result:
            stats = result['stats']
            print(f"  📹 {result['description']} ({operation}):")
            print(f"    - 处理时间: {result['processing_time']:.2f}秒")
            print(f"    - 平均FPS: {stats.get('average_fps', 0):.1f}")
            print(f"    - 峰值内存: {stats.get('peak_memory_mb', 0):.1f}MB")
            print(f"    - 输出大小: {result['output_size_mb']:.2f}MB")
        else:
            print(f"  ❌ {operation}: {result.get('error', 'Unknown error')}")
    
    # 清理测试文件
    if os.path.exists(test_video):
        os.remove(test_video)
        print(f"\n🗑️  已清理测试文件: {test_video}")
    
    return results


if __name__ == "__main__":
    try:
        # 运行复杂操作测试
        results = test_multiple_operations()
        
        print("\n🎉 所有测试完成！")
        print("✨ 视频处理性能优化验证成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

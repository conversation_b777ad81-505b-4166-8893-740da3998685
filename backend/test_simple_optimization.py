#!/usr/bin/env python3
"""
简化的视频处理性能测试
验证并行处理优化效果
"""

import sys
import os
import time
import cv2
import numpy as np

# 添加项目路径
sys.path.append('.')

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, PerformanceConfig, create_opencv_processor


def create_simple_test_video(output_path: str, frames: int = 60):
    """创建简单的测试视频"""
    print(f"Creating test video with {frames} frames...")
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, 30, (640, 480))
    
    for i in range(frames):
        # 创建简单的测试帧
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame[:, :] = (i * 4 % 255, 100, 150)  # 变化的颜色
        
        # 添加文字
        cv2.putText(frame, f'Frame {i}', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print(f"Test video created: {output_path}")


def test_optimization():
    """测试优化效果"""
    print("🚀 视频处理优化测试")
    print("=" * 50)
    
    # 创建测试视频
    test_video = "simple_test.mp4"
    if not os.path.exists(test_video):
        create_simple_test_video(test_video, frames=90)  # 3秒视频
    
    # 测试灰度化处理
    print("\n📹 测试灰度化处理...")
    
    # 创建OpenCV处理器
    opencv_processor = create_opencv_processor('grayscale')
    
    # 创建性能配置
    perf_config = PerformanceConfig()
    print(f"配置: {perf_config.max_workers} workers, batch_size={perf_config.batch_size}")
    
    # 进度回调
    def progress_callback(progress, total, message):
        print(f"  进度: {progress}% - {message}")
    
    # 测试处理
    start_time = time.time()
    
    try:
        with OpenCVFFmpegProcessor(test_video, preserve_audio=False, performance_config=perf_config) as processor:
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix='test_grayscale',
                progress_callback=progress_callback
            )
        
        processing_time = time.time() - start_time
        
        print(f"\n✅ 处理完成!")
        print(f"⏱️  总时间: {processing_time:.2f}秒")
        
        # 显示性能统计
        if 'performance_stats' in result:
            stats = result['performance_stats']
            print(f"📊 性能统计:")
            print(f"   - 平均FPS: {stats.get('average_fps', 0):.1f}")
            print(f"   - 峰值内存: {stats.get('peak_memory_mb', 0):.1f}MB")
            print(f"   - 使用线程: {stats.get('workers_used', 1)}")
            print(f"   - 批处理大小: {stats.get('batch_size', 'N/A')}")
        
        print(f"📁 输出文件: {result['output_path']}")
        
        # 验证输出文件
        if os.path.exists(result['output_path']):
            file_size = os.path.getsize(result['output_path']) / 1024 / 1024
            print(f"📏 文件大小: {file_size:.2f}MB")
            
            # 清理输出文件
            os.remove(result['output_path'])
            print("🗑️  已清理输出文件")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 清理测试文件
    if os.path.exists(test_video):
        os.remove(test_video)
        print("🗑️  已清理测试文件")


def test_system_info():
    """显示系统信息"""
    print("\n💻 系统信息")
    print("-" * 30)
    
    import multiprocessing
    import psutil
    
    print(f"CPU核心数: {multiprocessing.cpu_count()}")
    print(f"可用内存: {psutil.virtual_memory().available / 1024 / 1024 / 1024:.1f}GB")
    print(f"总内存: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB")
    
    # 测试OpenCV版本
    print(f"OpenCV版本: {cv2.__version__}")


if __name__ == "__main__":
    try:
        # 显示系统信息
        test_system_info()
        
        # 运行优化测试
        test_optimization()
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

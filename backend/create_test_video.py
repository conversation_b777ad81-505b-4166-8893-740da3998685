#!/usr/bin/env python3
"""
创建测试视频文件用于前后端集成测试
"""

import cv2
import numpy as np
import os

def create_test_video_for_frontend():
    """创建用于前端测试的视频文件"""
    output_path = "test_video_for_frontend.mp4"
    
    print(f"Creating test video: {output_path}")
    
    # 创建一个中等大小的测试视频
    width, height = 854, 480  # 480p
    fps = 30
    duration = 3  # 3秒
    total_frames = fps * duration
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    for i in range(total_frames):
        # 创建彩色渐变背景
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 创建渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    int(255 * (x / width)),  # Red gradient
                    int(255 * (y / height)),  # Green gradient
                    int(255 * ((i % 30) / 30))  # Blue animation
                ]
        
        # 添加一些图形
        cv2.rectangle(frame, (100, 100), (300, 200), (255, 255, 255), 3)
        cv2.circle(frame, (width//2, height//2), 50, (0, 255, 255), -1)
        
        # 添加文字
        cv2.putText(frame, f'Frame {i+1}/{total_frames}', (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(frame, 'Performance Test Video', (50, height-50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    
    # 获取文件信息
    file_size = os.path.getsize(output_path) / 1024 / 1024  # MB
    print(f"Test video created: {output_path}")
    print(f"Duration: {duration}s, Frames: {total_frames}, Size: {file_size:.2f}MB")
    
    return output_path

if __name__ == "__main__":
    create_test_video_for_frontend()

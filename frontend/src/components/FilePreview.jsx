import React, { useState, useEffect } from 'react'
import { Eye, X, Download, Play, Loader } from 'lucide-react'
import './FilePreview.css'
import { useApi } from '../contexts/ApiContext'

const FilePreview = ({ file, onRemove, showPreview = true }) => {
  const [previewUrl, setPreviewUrl] = useState(null)
  const [showModal, setShowModal] = useState(false)
  const [thumbnailLoading, setThumbnailLoading] = useState(false)
  const [thumbnailError, setThumbnailError] = useState(false)
  const { apiBase } = useApi()

  // 检查是否为视频文件
  const isVideoFile = (file) => {
    return file.type?.startsWith('video/') ||
           /\.(mp4|avi|mov|mkv|wmv|flv|webm|m4v)$/i.test(file.name)
  }

  // 生成预览URL
  useEffect(() => {
    if (file && showPreview) {
      if (file.type?.startsWith('image/')) {
        // 图像文件：直接创建预览URL
        const url = URL.createObjectURL(file)
        setPreviewUrl(url)
        return () => URL.revokeObjectURL(url)
      }
      // 视频文件：不生成预览，显示提示信息
    }
  }, [file, showPreview])

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = () => {
    if (file.type?.startsWith('image/')) {
      return '🖼️'
    } else if (isVideoFile(file)) {
      return '🎥'
    }
    return '📄'
  }

  // 渲染缩略图内容
  const renderThumbnailContent = () => {
    if (previewUrl) {
      return (
        <img
          src={previewUrl}
          alt={file.name}
          className="thumbnail-image"
          onClick={() => setShowModal(true)}
          onError={() => {
            if (isVideoFile(file)) {
              setThumbnailError(true)
              setPreviewUrl(null)
            }
          }}
        />
      )
    }

    if (thumbnailLoading && isVideoFile(file)) {
      return (
        <div className="thumbnail-loading">
          <Loader className="loading-spinner" />
          <span className="loading-text">生成缩略图...</span>
        </div>
      )
    }

    if (thumbnailError && isVideoFile(file)) {
      return (
        <div className="thumbnail-error">
          <Play className="error-icon" />
          <span className="error-text">缩略图生成失败</span>
        </div>
      )
    }

    return (
      <div className="file-icon">
        {getFileIcon()}
      </div>
    )
  }

  return (
    <>
      <div className="file-preview">
        <div className="file-preview-content">
          {/* 文件缩略图或图标 */}
          <div className="file-thumbnail">
            {renderThumbnailContent()}
            {(previewUrl || (isVideoFile(file) && !thumbnailError)) && (
              <button
                className="preview-btn"
                onClick={() => setShowModal(true)}
                title="预览"
                disabled={thumbnailLoading}
              >
                <Eye className="btn-icon" />
              </button>
            )}
          </div>

          {/* 文件信息 */}
          <div className="file-info">
            <div className="file-name" title={file.name}>
              {file.name}
            </div>
            <div className="file-meta">
              <span className="file-size">{formatFileSize(file.size)}</span>
              <span className="file-type">{file.type || 'unknown'}</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="file-actions">
            {onRemove && (
              <button
                className="btn-icon-only remove-file"
                onClick={() => onRemove()}
                title="移除文件"
              >
                <X className="btn-icon" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 预览模态框 */}
      {showModal && previewUrl && (
        <div className="preview-modal" onClick={() => setShowModal(false)}>
          <div className="preview-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="preview-header">
              <h3 className="preview-title">{file.name}</h3>
              <button 
                className="btn-icon-only close-modal"
                onClick={() => setShowModal(false)}
              >
                <X className="btn-icon" />
              </button>
            </div>
            <div className="preview-body">
              {isVideoFile(file) ? (
                <div className="video-preview">
                  <img
                    src={previewUrl}
                    alt={file.name}
                    className="preview-image"
                  />
                  <div className="video-overlay">
                    <Play className="play-icon" />
                    <span className="video-label">视频预览</span>
                  </div>
                </div>
              ) : (
                <img
                  src={previewUrl}
                  alt={file.name}
                  className="preview-image"
                />
              )}
            </div>
            <div className="preview-footer">
              <div className="file-details">
                <span>大小: {formatFileSize(file.size)}</span>
                <span>类型: {file.type}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default FilePreview
